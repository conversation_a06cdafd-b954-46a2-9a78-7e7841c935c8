#include <pcl/io/pcd_io.h>
#include <pcl/point_types.h>
#include <pcl/search/kdtree.h>
#include <pcl/features/normal_3d_omp.h>
#include <boost/filesystem.hpp>
#include <boost/algorithm/string/predicate.hpp>
#include <omp.h>
#include <iostream>
#include <vector>
#include <algorithm>
#include <cmath>
#include <limits>

namespace fs = boost::filesystem;

// ======== Params ========
static const float RADIUS = 0.3f;        // 邻域半径 (m)：用于高差/密度/法线估计
static const float HEIGHT_P95_FLOOR = 0.0025f; // R 通道映射上限的保底值 height 0.2 
static const int   DENSITY_P95_FLOOR = 50;   // G 通道映射上限的保底值
static const bool  USE_LOG_DENSITY = false;   // 密度是否使用对数映射
static const int   RGB_LIMIT = 20;

static inline uint8_t linear_to_u8(float v, float vmin, float vmax) {
    if (!std::isfinite(v)) return 0;
    if (v <= vmin) return 0;
    if (v >= vmax) return 255;
    float x = (v - vmin) / (vmax - vmin);
    return static_cast<uint8_t>(std::round(x * 255.0f));
}

static inline uint8_t log_to_u8(int n, int nmax) {
    if (n <= 0) return 0;
    if (n >= nmax) return 255;
    const float ln_num = std::log(1.0f + static_cast<float>(n));
    const float ln_den = std::log(1.0f + static_cast<float>(nmax));
    float x = (ln_den > 0.0f) ? (ln_num / ln_den) : 0.0f;
    if (x < 0.0f) x = 0.0f;
    if (x > 1.0f) x = 1.0f;
    return static_cast<uint8_t>(std::round(x * 255.0f));
}


template <typename T>
T percentile_copy(const std::vector<T>& v, double p) {
    if (v.empty()) return T(0);
    p = std::min(std::max(p, 0.0), 1.0);
    size_t k = static_cast<size_t>(std::floor(p * (v.size() - 1)));
    std::vector<T> tmp(v);
    std::nth_element(tmp.begin(), tmp.begin() + k, tmp.end());
    return tmp[k];
}

void processPCD(const std::string& input_file, const std::string& output_dir) {
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
    if (pcl::io::loadPCDFile<pcl::PointXYZRGB>(input_file, *cloud) == -1 || cloud->empty()) {
        std::cerr << "Error: Failed to load point cloud or cloud is empty: " << input_file << std::endl;
        return;
    }
    std::cout << "Processing: " << input_file << " (" << cloud->size() << " points)" << std::endl;

    const size_t N = cloud->size();

    // KdTree
    pcl::search::KdTree<pcl::PointXYZRGB>::Ptr tree(new pcl::search::KdTree<pcl::PointXYZRGB>);
    tree->setInputCloud(cloud);

    pcl::PointCloud<pcl::Normal>::Ptr normals(new pcl::PointCloud<pcl::Normal>);
    normals->resize(N);
    {
        pcl::NormalEstimationOMP<pcl::PointXYZRGB, pcl::Normal> ne;
        ne.setNumberOfThreads(omp_get_max_threads() / 2);
        ne.setInputCloud(cloud);
        ne.setSearchMethod(tree);
        ne.setRadiusSearch(RADIUS);
        ne.compute(*normals);
    }

    std::vector<float> height_diff(N, 0.0f);
    std::vector<int>   density(N, 0);

    #pragma omp parallel for schedule(dynamic)
    for (long long i = 0; i < static_cast<long long>(N); ++i) {
        std::vector<int> idx;
        std::vector<float> dist2;
        const auto& p = cloud->points[static_cast<size_t>(i)];
        // if (tree->radiusSearch(p, RADIUS, idx, dist2) > 0) {
        //     density[i] = static_cast<int>(idx.size());
        //     float minz = p.z, maxz = p.z;
        //     for (int j : idx) {
        //         float z = cloud->points[static_cast<size_t>(j)].z;
        //         if (z < minz) minz = z;
        //         if (z > maxz) maxz = z;
        //     }
        //     height_diff[i] = (maxz - minz);
        // } else {
        //     density[i] = 0;
        //     height_diff[i] = 0.0f;
        // }

        if (tree->radiusSearch(p, RADIUS, idx, dist2) > 0) {
            density[i] = static_cast<int>(idx.size());
            const int nn = density[i];

            double sumz = 0.0;
            for (int j : idx) sumz += cloud->points[j].z;
            double mean = sumz / std::max(1, nn);

            double acc = 0.0;
            for (int j : idx) {
                double diff = cloud->points[j].z - mean;
                acc += diff * diff;
            }
            double var = (nn > 0) ? acc / nn : 0.0;    
            double std = std::sqrt(std::max(0.0, var)); 

            height_diff[i] = static_cast<float>(std);
        } else {
            density[i] = 0;
            height_diff[i] = 0.0f;
        }
    }

    float h95 = percentile_copy(height_diff, 0.95);
    int   d95 = percentile_copy(density, 0.95);
    float height_clamp  = std::max(HEIGHT_P95_FLOOR, h95);
    int   density_clamp = std::max(DENSITY_P95_FLOOR, d95);

    std::cout << "  P95(Δh) = " << h95 << " m, clamp = " << height_clamp
              << " | P95(density) = " << d95 << ", clamp = " << density_clamp << std::endl;


    std::vector<float> avg_thresholds = {0.20f, 0.30f, 0.40f, 0.50f, 0.65f};  
    std::vector<pcl::PointCloud<pcl::PointXYZRGB>::Ptr> selected_clouds(avg_thresholds.size());
    for (size_t t = 0; t < avg_thresholds.size(); ++t) {
        selected_clouds[t].reset(new pcl::PointCloud<pcl::PointXYZRGB>);
        selected_clouds[t]->reserve(cloud->size());
    }
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr colored(new pcl::PointCloud<pcl::PointXYZRGB>);
    colored->reserve(N);

    for (size_t i = 0; i < N; ++i) {
        pcl::PointXYZRGB q;
        const auto& p = cloud->points[i];
        q.x = p.x; q.y = p.y; q.z = p.z;

        // R: 局部高差
        uint8_t r = linear_to_u8(height_diff[i], 0.0f, height_clamp);

        // G: 邻域密度
        uint8_t g = USE_LOG_DENSITY
                  ? log_to_u8(density[i], density_clamp)
                  : linear_to_u8(static_cast<float>(density[i]), 0.0f, static_cast<float>(density_clamp));

        // B: 法线与竖直方向夹角（0°->0, 90°->255）
        const pcl::Normal& n = (*normals)[i];
        uint8_t b = 0;
        if (pcl::isFinite(n)) {
            float dot = std::fabs(n.normal_z);                     
            dot = std::max(-1.0f, std::min(1.0f, dot));
            float angle_rad = std::acos(dot);                      
            float angle_deg = angle_rad * 180.0f / static_cast<float>(M_PI);
            b = linear_to_u8(angle_deg, 0.0f, 90.0f);
        } else {
            b = 0;
        }

        q.r = r; q.g = g; q.b = b;
        colored->push_back(q);

        float avg_score = (r + g + b) / 765.0f;

        for (size_t t = 0; t < avg_thresholds.size(); ++t) {
            if (q.r < RGB_LIMIT || q.g < RGB_LIMIT || q.b < RGB_LIMIT) continue;
            if (avg_score >= avg_thresholds[t]) {
                selected_clouds[t]->push_back(q);  
            }
        }
    }

    colored->width = static_cast<uint32_t>(colored->size());
    colored->height = 1;
    colored->is_dense = false;
    colored->header = cloud->header;

    if (!fs::exists(output_dir)) fs::create_directories(output_dir);
    fs::path input_path(input_file);
    std::string stem = input_path.stem().string();
    std::string out_path = (fs::path(output_dir) / (stem + "_rgb.pcd")).string();
    pcl::io::savePCDFileBinary(out_path, *colored);
    std::cout << "  Saved RGB-mapped cloud (" << colored->size()
              << ") to " << out_path << std::endl;

    for (size_t t = 0; t < avg_thresholds.size(); ++t) {
        auto& pc = selected_clouds[t];
        pc->width = static_cast<uint32_t>(pc->size());
        pc->height = 1;
        pc->is_dense = false;
        pc->header = cloud->header;

        int thr = static_cast<int>(std::round(avg_thresholds[t] * 100.0f));
        std::string out_sel = (fs::path(output_dir) / (stem + "_avgT" + std::to_string(thr) + ".pcd")).string();
        pcl::io::savePCDFileBinary(out_sel, *pc);
        std::cout << "  Saved selected (avg>=" << avg_thresholds[t] << "): "
                << pc->size() << " -> " << out_sel << std::endl;
    }
}

int main(int argc, char** argv) {
    if (argc < 3) {
        std::cerr << "Usage: " << argv[0] << " input_directory output_directory" << std::endl;
        return -1;
    }

    std::string input_dir = argv[1];
    std::string output_dir = argv[2];
    if (!fs::exists(output_dir)) fs::create_directories(output_dir);

    std::vector<std::string> pcd_files;
    fs::directory_iterator end_iter;
    for (fs::directory_iterator it(input_dir); it != end_iter; ++it) {
        if (fs::is_regular_file(it->status())) {
            const std::string ext = fs::extension(it->path());
            if (boost::iequals(ext, ".pcd")) pcd_files.push_back(it->path().string());
        }
    }
    if (pcd_files.empty()) {
        std::cerr << "Error: No PCD files found in input directory: " << input_dir << std::endl;
        return -1;
    }

    std::cout << "Found " << pcd_files.size() << " PCD files to process" << std::endl;
    for (const auto& f : pcd_files) processPCD(f, output_dir);

    std::cout << "Processing completed. Results saved to: " << output_dir << std::endl;
    return 0;
}
