import os
import sys
import argparse
import time
from typing import Optional, Tuple

import numpy as np
import laspy 
import CSF  
import open3d as o3d  


# ======== Parameters ========
RADIUS = 0.3  # meters
HEIGHT_P95_FLOOR = 0.0025
DENSITY_P95_FLOOR = 50
USE_LOG_DENSITY = False
RGB_LIMIT = 20
GB_RATIO = 1.2


def linear_to_u8(v: float, vmin: float, vmax: float) -> int:
    if not np.isfinite(v):
        return 0
    if v <= vmin:
        return 0
    if v >= vmax:
        return 255
    x = (v - vmin) / (vmax - vmin)
    return int(round(x * 255.0))


def log_to_u8(n: int, nmax: int) -> int:
    if n <= 0:
        return 0
    if n >= nmax:
        return 255
    ln_num = np.log(1.0 + float(n))
    ln_den = np.log(1.0 + float(nmax))
    x = (ln_num / ln_den) if ln_den > 0.0 else 0.0
    x = max(0.0, min(1.0, x))
    return int(round(x * 255.0))


def percentile_copy(arr: np.ndarray, p: float):
    if arr.size == 0:
        return 0
    p = min(max(p, 0.0), 1.0)
    return float(np.percentile(arr, p * 100.0))


def read_las_points_with_rgb(las_path: str) -> Tuple[np.ndarray, Optional[np.ndarray], object, object]:
    las = laspy.read(las_path)
    xyz = np.vstack((las.x, las.y, las.z)).T.astype(np.float64)

    colors = None
    if hasattr(las, "red") and hasattr(las, "green") and hasattr(las, "blue"):
        max_rgb = max(las.red.max(initial=0), las.green.max(initial=0), las.blue.max(initial=0))
        denom = 65535.0 if max_rgb and max_rgb > 255 else 255.0
        colors = np.vstack((las.red / denom, las.green / denom, las.blue / denom)).T
        colors = np.clip(colors, 0.0, 1.0).astype(np.float64)

    return xyz, colors, las.header, las.points


def write_las_from_subset(header, points, mask: np.ndarray, out_path: str):
    out = laspy.LasData(header)
    out.points = points[mask]
    out.write(out_path)


def las_to_pcd_with_rgb(las_file_path: str, pcd_file_path: str):
    las = laspy.read(las_file_path)
    pts = np.vstack((las.x, las.y, las.z)).T.astype(np.float64)
    if hasattr(las, "red") and hasattr(las, "green") and hasattr(las, "blue"):
        max_rgb = max(las.red.max(initial=0), las.green.max(initial=0), las.blue.max(initial=0))
        denom = 65535.0 if max_rgb and max_rgb > 255 else 255.0
        colors = np.vstack((las.red / denom, las.green / denom, las.blue / denom)).T
        colors = np.clip(colors, 0.0, 1.0).astype(np.float64)
    else:
        colors = np.ones((pts.shape[0], 3), dtype=np.float64)

    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(pts)
    pcd.colors = o3d.utility.Vector3dVector(colors)
    o3d.io.write_point_cloud(pcd_file_path, pcd, write_ascii=False, compressed=False)


def run_csf_ground_filter(xyz: np.ndarray, csf_params: dict) -> Tuple[np.ndarray, np.ndarray]:
    csf = CSF.CSF()
    csf.params.bSloopSmooth = bool(csf_params.get("bSloopSmooth", True))
    csf.params.cloth_resolution = float(csf_params.get("cloth_resolution", 2.0))
    csf.params.rigidness = int(csf_params.get("rigidness", 3))
    csf.params.class_threshold = float(csf_params.get("class_threshold", 0.25))
    csf.setPointCloud(xyz.astype(np.float64))

    ground = CSF.VecInt()
    non_ground = CSF.VecInt()
    csf.do_filtering(ground, non_ground)

    ground_idx = np.array(list(ground), dtype=np.int64)
    non_ground_idx = np.array(list(non_ground), dtype=np.int64)
    return ground_idx, non_ground_idx


def compute_normals_density_height(pcd: o3d.geometry.PointCloud, radius: float) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    # Estimate normals
    pcd.estimate_normals(search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=radius, max_nn=1000))
    pcd.orient_normals_consistent_tangent_plane(10)

    pts = np.asarray(pcd.points)
    N = pts.shape[0]

    # KDTree for neighbor queries
    kdtree = o3d.geometry.KDTreeFlann(pcd)

    density = np.zeros(N, dtype=np.int32)
    height_std = np.zeros(N, dtype=np.float64)

    for i in range(N):
        _, idx, _ = kdtree.search_radius_vector_3d(pts[i], radius)
        density[i] = len(idx)
        if density[i] > 0:
            zs = pts[idx, 2]
            height_std[i] = float(np.std(zs))
        else:
            height_std[i] = 0.0

    normals = np.asarray(pcd.normals)
    return normals, density, height_std


def map_to_rgb_and_select(pts: np.ndarray,
                          normals: np.ndarray,
                          density: np.ndarray,
                          height_std: np.ndarray,
                          use_log_density: bool = USE_LOG_DENSITY,
                          rgb_limit: int = RGB_LIMIT) -> Tuple[o3d.geometry.PointCloud, dict]:
    h95 = percentile_copy(height_std, 0.95)
    d95 = percentile_copy(density, 0.95)
    height_clamp = max(HEIGHT_P95_FLOOR, h95)
    density_clamp = int(max(DENSITY_P95_FLOOR, d95))

    N = pts.shape[0]
    colors_u8 = np.zeros((N, 3), dtype=np.uint8)
    angles_deg = np.degrees(np.arccos(np.clip(np.abs(normals[:, 2]), -1.0, 1.0)))  # 0-180
    angles_deg = np.clip(angles_deg, 0.0, 90.0)

    # Map channels
    r = np.array([linear_to_u8(v, 0.0, height_clamp) for v in height_std], dtype=np.uint8)
    if use_log_density:
        g = np.array([log_to_u8(int(v), int(density_clamp)) for v in density], dtype=np.uint8)
    else:
        g = np.array([linear_to_u8(float(v), 0.0, float(density_clamp)) for v in density], dtype=np.uint8)
    b = np.array([linear_to_u8(v, 0.0, 90.0) for v in angles_deg], dtype=np.uint8)

    colors_u8[:, 0] = r
    colors_u8[:, 1] = g
    colors_u8[:, 2] = b

    # Colored cloud
    colored = o3d.geometry.PointCloud()
    colored.points = o3d.utility.Vector3dVector(pts)
    colored.colors = o3d.utility.Vector3dVector(colors_u8.astype(np.float64) / 255.0)

    # Selections by average threshold
    avg_thresholds = [0.20, 0.30, 0.40, 0.50, 0.65]
    selections = {}
    rgb_valid = (r >= rgb_limit) & (g >= rgb_limit) & (b >= rgb_limit)
    avg_score = (r.astype(np.float64) + g.astype(np.float64) + b.astype(np.float64)) / (3.0 * 255.0)

    for thr in avg_thresholds:
        mask = rgb_valid & (avg_score >= thr)
        sel = o3d.geometry.PointCloud()
        sel.points = o3d.utility.Vector3dVector(pts[mask])
        sel.colors = o3d.utility.Vector3dVector((colors_u8[mask].astype(np.float64) / 255.0))
        selections[thr] = sel

    stats = {
        "h95": float(h95),
        "height_clamp": float(height_clamp),
        "d95": int(d95),
        "density_clamp": int(density_clamp),
    }
    return colored, selections, stats


def process_single_point_cloud(input_path: str, output_dir: str, radius: float = RADIUS) -> None:
    os.makedirs(output_dir, exist_ok=True)
    base = os.path.splitext(os.path.basename(input_path))[0]

    xyz, colors, header, points = read_las_points_with_rgb(input_path)

    # RGB G/B filtering
    normal_mask = np.ones(xyz.shape[0], dtype=bool)
    if colors is not None and hasattr(points, 'blue') and hasattr(points, 'green'):
        blue = points.blue
        green = points.green
        blue_safe = np.where(blue == 0, 1, blue)
        gb_ratio = green / blue_safe
        high_ratio_mask = gb_ratio > GB_RATIO
        normal_mask = ~high_ratio_mask
        if high_ratio_mask.any():
            high_ratio_path = os.path.join(output_dir, f"{base}_high_gb_ratio.las")
            write_las_from_subset(header, points, high_ratio_mask, high_ratio_path)

    xyz_filtered = xyz[normal_mask]
    pts_for_processing = xyz_filtered

    # CSF Ground Filtering
    ground_idx, non_ground_idx = run_csf_ground_filter(
        pts_for_processing,
        csf_params=dict(bSloopSmooth=True, cloth_resolution=2.0, rigidness=3, class_threshold=0.25),
    )
    ground_mask_full = normal_mask.copy()
    ground_mask_full[normal_mask] = False 
    ground_mask_filtered = np.zeros(pts_for_processing.shape[0], dtype=bool)
    ground_mask_filtered[ground_idx] = True
    ground_mask_full[np.where(normal_mask)[0]] = ground_mask_filtered
    csf_out_path = os.path.join(output_dir, f"{base}_csf.las")
    write_las_from_subset(header, points, ground_mask_full, csf_out_path)

    pts_for_processing = pts_for_processing[ground_idx]
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(pts_for_processing)

    # Compute normals, density, height std
    normals, density, height_std = compute_normals_density_height(pcd, radius)

    # Map to RGB and select
    colored, selections, stats = map_to_rgb_and_select(pts_for_processing, normals, density, height_std)

    # Save outputs similar to segment.cpp
    rgb_path = os.path.join(output_dir, f"{base}_rgb.pcd")
    o3d.io.write_point_cloud(rgb_path, colored, write_ascii=False, compressed=False)

    for thr, sel_pc in selections.items():
        thr_int = int(round(thr * 100.0))
        out_sel = os.path.join(output_dir, f"{base}_avgT{thr_int}.pcd")
        o3d.io.write_point_cloud(out_sel, sel_pc, write_ascii=False, compressed=False)

    print(f"P95(Δh)={stats['h95']:.6f} clamp={stats['height_clamp']:.6f} | P95(density)={stats['d95']} clamp={stats['density_clamp']}")
    print(f"Saved colored: {rgb_path}")
    for thr in sorted(selections.keys()):
        thr_int = int(round(thr * 100.0))
        print(f"Saved selection avg>={thr:.2f}: {os.path.join(output_dir, f'{base}_avgT{thr_int}.pcd')}")


def main():
    parser = argparse.ArgumentParser(description="Merge CSF ground extraction and RGB mapping for a single point cloud (LAS/PCD)")
    parser.add_argument("--input", required=True, help="Path to input .las or .pcd")
    parser.add_argument("--output_dir", required=True, help="Directory to save outputs")
    parser.add_argument("--radius", type=float, default=RADIUS, help="Neighborhood radius for normals/density/height (m)")
    args = parser.parse_args()

    t0 = time.time()
    process_single_point_cloud(args.input, args.output_dir, radius=args.radius)
    print(f"Done in {time.time() - t0:.2f}s")


if __name__ == "__main__":
    main()

