import laspy
import CSF
import numpy as np
import os
from concurrent.futures import ThreadPoolExecutor
import time
import open3d as o3d


def las_to_pcd_with_rgb(las_file_path, pcd_file_path):
     las = laspy.read(las_file_path)
     pts = np.vstack((las.x, las.y, las.z)).T.astype(np.float64)
 
     if hasattr(las, "red") and hasattr(las, "green") and hasattr(las, "blue"):
         max_rgb = max(las.red.max(), las.green.max(), las.blue.max())
         denom = 65535.0 if max_rgb > 255 else 255.0
         colors = np.vstack((las.red/denom, las.green/denom, las.blue/denom)).T
         colors = np.clip(colors, 0.0, 1.0).astype(np.float64)
     else:
         colors = np.ones((pts.shape[0], 3), dtype=np.float64)
 
     pcd = o3d.geometry.PointCloud()
     pcd.points = o3d.utility.Vector3dVector(pts)
     pcd.colors = o3d.utility.Vector3dVector(colors)
     o3d.io.write_point_cloud(pcd_file_path, pcd, write_ascii=False, compressed=False)

def process_las_file(input_path, output_folder):
    try:
        start_time = time.time()
        inFile = laspy.read(input_path)
        points = inFile.points
        xyz = np.vstack((inFile.x, inFile.y, inFile.z)).transpose()
        
        # RGB过滤：计算G/B比值，超过1.2的点单独保存
        if hasattr(inFile, "red") and hasattr(inFile, "green") and hasattr(inFile, "blue"):
            # 避免除零错误，将B为0的点设为很小的值
            blue_safe = np.where(inFile.blue == 0, 1, inFile.blue)
            gb_ratio = inFile.green / blue_safe
            
            high_ratio_mask = gb_ratio > 1.2
            normal_mask = ~high_ratio_mask
            
            print(f"G/B比值>1.2的点数: {np.sum(high_ratio_mask)}, 正常点数: {np.sum(normal_mask)}")
            
            if np.sum(high_ratio_mask) > 0:
                high_ratio_file = laspy.LasData(inFile.header)
                high_ratio_file.points = points[high_ratio_mask]
                
                base_name = os.path.basename(input_path)
                high_ratio_name = os.path.splitext(base_name)[0] + "_high_gb_ratio.las"
                high_ratio_path = os.path.join(output_folder, high_ratio_name)
                high_ratio_file.write(high_ratio_path)
                print(f"保存高G/B比值点云: {high_ratio_path}")
            
            filtered_points = points[normal_mask]
            filtered_xyz = xyz[normal_mask]
        else:
            print("警告: LAS文件不包含RGB信息，跳过RGB过滤")
            filtered_points = points
            filtered_xyz = xyz
        
        csf = CSF.CSF()
        csf.params.bSloopSmooth = True
        csf.params.cloth_resolution = 2.0 # 越大越粗糙
        csf.params.rigidness = 3 # 越大布越硬，不容易贴合复杂地形
        csf.params.class_threshold = 0.25 # 决定点云中布和点的距离是否判为地面
        csf.setPointCloud(filtered_xyz)
        
        ground = CSF.VecInt()
        non_ground = CSF.VecInt()
        csf.do_filtering(ground, non_ground)
        
        outFile = laspy.LasData(inFile.header)
        outFile.points = filtered_points[np.array(ground)]
        
        base_name = os.path.basename(input_path)
        output_name = os.path.splitext(base_name)[0] + "_csf.las"
        output_path = os.path.join(output_folder, output_name)
        
        outFile.write(output_path)
        
        pcd_name = os.path.splitext(base_name)[0] + "_csf.pcd"
        pcd_path = os.path.join(output_folder, pcd_name)
        las_to_pcd_with_rgb(output_path, pcd_path)
        
        elapsed = time.time() - start_time
        print(f"Processed {base_name} in {elapsed:.2f}s -> {output_path}")
        return True
    except Exception as e:
        print(f"Error processing {input_path}: {str(e)}")
        return False

def process_folder(input_folder, output_folder, max_workers=4):
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    las_files = []
    for file_name in os.listdir(input_folder):
        if file_name.lower().endswith('.las'):
            input_path = os.path.join(input_folder, file_name)
            las_files.append(input_path)
    
    if not las_files:
        print("No LAS files found in the input folder.")
        return
    
    print(f"Found {len(las_files)} LAS files to process. Using {max_workers} threads...")
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for input_path in las_files:
            future = executor.submit(process_las_file, input_path, output_folder)
            futures.append(future)
        
        success_count = sum(f.result() for f in futures if f.result() is not None)
    
    print(f"\nProcessing completed. Success: {success_count}/{len(las_files)} files")

if __name__ == "__main__":
    input_folder = "/home/<USER>/下载/Scanner1A20/" 
    output_folder = "/home/<USER>/下载/Scanner1A20/csf/"  
    max_workers = 4  
    
    process_folder(input_folder, output_folder, max_workers)
