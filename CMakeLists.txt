cmake_minimum_required(VERSION 3.0.0)
project(guitu)
set(CMAKE_BUILD_TYPE DEBUG)

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED TRUE)
include_directories(../common/include)
find_package(Boost REQUIRED COMPONENTS filesystem)
find_package(PCL 1.8 REQUIRED)
find_package(OpenCV REQUIRED)

find_package(OpenMP REQUIRED)

include_directories(${CMAKE_SOURCE_DIR}/common/include)
include_directories(${PCL_INCLUDE_DIRS} ${Boost_INCLUDE_DIRS} ${OpenCV_INCLUDE_DIRS})
link_directories(${PCL_LIBRARY_DIRS})
add_definitions(${PCL_DEFINITIONS})


add_executable(plane_extraction plane_extraction.cpp)
target_link_libraries(plane_extraction ${PCL_LIBRARIES} )

add_executable(batch_height_diff batch_height_diff.cpp)
target_link_libraries(batch_height_diff ${PCL_LIBRARIES})

add_executable(dem_fusion dem_fusion.cpp)
target_link_libraries(dem_fusion ${PCL_LIBRARIES})

add_executable(segment segment.cpp)
target_link_libraries(segment ${PCL_LIBRARIES} OpenMP::OpenMP_CXX) 